<div>
    <form wire:submit.prevent="store">
        <?php if (isset($component)) { $__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.row','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.row'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
            <div class="col">
                <?php if (isset($component)) { $__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card.body','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('card.body'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Email','labelRequired' => '1','model' => 'user.email']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Email','labelRequired' => '1','model' => 'user.email']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                        </div>

                        <div class="col-md-12 mb-3">
                            <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'First Name','labelRequired' => '1','model' => 'user.first_name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'First Name','labelRequired' => '1','model' => 'user.first_name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                        </div>

                        <div class="col-md-12 mb-3">
                            <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Last Name','labelRequired' => '1','model' => 'user.last_name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Last Name','labelRequired' => '1','model' => 'user.last_name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                        </div>

                        <div class="col-md-12 mb-3">
                            <div wire:ignore>
                                <?php if (isset($component)) { $__componentOriginalacec2bdb8f1dd627902b3a2fdc2d57eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalacec2bdb8f1dd627902b3a2fdc2d57eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.drop-down','data' => ['label' => 'Access Type','labelRequired' => '1','model' => 'user.role','id' => 'user.role']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.drop-down'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Access Type','labelRequired' => '1','model' => 'user.role','id' => 'user.role']); ?>
                                    <option value="">Select Access Type</option>
                                    <option value="<?php echo e(\App\Models\User::ROLE_ADMIN); ?>">Administrator</option>
                                    <option value="<?php echo e(\App\Models\User::ROLE_OPERATOR); ?>">Operator</option>
                                    <option value="<?php echo e(\App\Models\User::ROLE_PRACTICE_ADMIN); ?>">Practice Admin</option>
                                    <option value="<?php echo e(\App\Models\User::ROLE_PHARMACY_OPERATOR); ?>">Pharmacy Operator
                                    </option>
                                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalacec2bdb8f1dd627902b3a2fdc2d57eb)): ?>
<?php $attributes = $__attributesOriginalacec2bdb8f1dd627902b3a2fdc2d57eb; ?>
<?php unset($__attributesOriginalacec2bdb8f1dd627902b3a2fdc2d57eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalacec2bdb8f1dd627902b3a2fdc2d57eb)): ?>
<?php $component = $__componentOriginalacec2bdb8f1dd627902b3a2fdc2d57eb; ?>
<?php unset($__componentOriginalacec2bdb8f1dd627902b3a2fdc2d57eb); ?>
<?php endif; ?>
                            </div>
                            <?php if (isset($component)) { $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.error','data' => ['model' => 'user.role']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['model' => 'user.role']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $attributes = $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $component = $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
                        </div>

                        <?php if($user['role'] == \App\Models\User::ROLE_PHARMACY_OPERATOR): ?>
                            <div class="col-md-12 mb-3 form-group">
                                <div wire:ignore>
                                    <?php if (isset($component)) { $__componentOriginalacec2bdb8f1dd627902b3a2fdc2d57eb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalacec2bdb8f1dd627902b3a2fdc2d57eb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.drop-down','data' => ['label' => 'Pharmacy','labelRequired' => '1','model' => 'user.pharmacy_id','id' => 'user.pharmacy_id']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.drop-down'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Pharmacy','labelRequired' => '1','model' => 'user.pharmacy_id','id' => 'user.pharmacy_id']); ?>
                                        <option value="">Select Pharmacy</option>
                                        <?php $__currentLoopData = $pharmacies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pharmacy): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($pharmacy->id); ?>"><?php echo e($pharmacy->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalacec2bdb8f1dd627902b3a2fdc2d57eb)): ?>
<?php $attributes = $__attributesOriginalacec2bdb8f1dd627902b3a2fdc2d57eb; ?>
<?php unset($__attributesOriginalacec2bdb8f1dd627902b3a2fdc2d57eb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalacec2bdb8f1dd627902b3a2fdc2d57eb)): ?>
<?php $component = $__componentOriginalacec2bdb8f1dd627902b3a2fdc2d57eb; ?>
<?php unset($__componentOriginalacec2bdb8f1dd627902b3a2fdc2d57eb); ?>
<?php endif; ?>
                                </div>
                                <?php if (isset($component)) { $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.error','data' => ['model' => 'user.pharmacy_id']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['model' => 'user.pharmacy_id']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $attributes = $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $component = $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>

                            </div>
                        <?php endif; ?>

                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6)): ?>
<?php $attributes = $__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6; ?>
<?php unset($__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6)): ?>
<?php $component = $__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6; ?>
<?php unset($__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginal2f4c20c75f25d521b59e2ab32e77183d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2f4c20c75f25d521b59e2ab32e77183d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.group.errors','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('group.errors'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2f4c20c75f25d521b59e2ab32e77183d)): ?>
<?php $attributes = $__attributesOriginal2f4c20c75f25d521b59e2ab32e77183d; ?>
<?php unset($__attributesOriginal2f4c20c75f25d521b59e2ab32e77183d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2f4c20c75f25d521b59e2ab32e77183d)): ?>
<?php $component = $__componentOriginal2f4c20c75f25d521b59e2ab32e77183d; ?>
<?php unset($__componentOriginal2f4c20c75f25d521b59e2ab32e77183d); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card.footer','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('card.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <?php if (isset($component)) { $__componentOriginald2555272519db269fb0587805fbfa3da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald2555272519db269fb0587805fbfa3da = $attributes; } ?>
<?php $component = App\View\Components\BtnWithLoading::resolve(['target' => 'store','text' => 'Save','type' => 'submit'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('btn-with-loading'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\BtnWithLoading::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald2555272519db269fb0587805fbfa3da)): ?>
<?php $attributes = $__attributesOriginald2555272519db269fb0587805fbfa3da; ?>
<?php unset($__attributesOriginald2555272519db269fb0587805fbfa3da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald2555272519db269fb0587805fbfa3da)): ?>
<?php $component = $__componentOriginald2555272519db269fb0587805fbfa3da; ?>
<?php unset($__componentOriginald2555272519db269fb0587805fbfa3da); ?>
<?php endif; ?>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0)): ?>
<?php $attributes = $__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0; ?>
<?php unset($__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0)): ?>
<?php $component = $__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0; ?>
<?php unset($__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0); ?>
<?php endif; ?>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c)): ?>
<?php $attributes = $__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c; ?>
<?php unset($__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c)): ?>
<?php $component = $__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c; ?>
<?php unset($__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c); ?>
<?php endif; ?>
    </form>
</div>

<?php $__env->startPush('styles'); ?>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        .select2-container .select2-selection--single {
            height: 38px;
            display: flex;
            align-items: center;
            padding: 6px 12px;
        }

        .select2-container .select2-selection--single .select2-selection__arrow {
            height: 100%;
            display: flex;
            align-items: center;
            padding-left: 8px;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        function initSelect2Once(selector, model) {
            const $el = $(selector);

            if (!$el.length || $el.hasClass('select2-hidden-accessible')) {
                // Element not found or already initialized
                return;
            }

            $el.select2({
                placeholder: "Select Option",
                width: '100%'
            }).on('change', function(e) {
                window.livewire.find('<?php echo e($_instance->id); ?>').set(model, $(this).val());
            });
        }

        document.addEventListener("livewire:load", function() {
            initSelect2Once('#user\\.role', 'user.role');
            initSelect2Once('#user\\.pharmacy_id', 'user.pharmacy_id');

            Livewire.hook('message.processed', (message, component) => {
                setTimeout(() => {
                    initSelect2Once('#user\\.role', 'user.role');
                    initSelect2Once('#user\\.pharmacy_id', 'user.pharmacy_id');
                }, 10); // Delay to ensure Livewire DOM is ready
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\KodeCreators\newlife-panel\resources\views/livewire/user/user-admin.blade.php ENDPATH**/ ?>