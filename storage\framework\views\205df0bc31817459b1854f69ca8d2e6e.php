

<?php $__env->startSection('content'); ?>
<div class="row">
  <div class="col-12 col-md-8 col-xl-6">
    <div class="card mb-8">

      <?php
if (! isset($_instance)) {
    $html = \Livewire\Livewire::mount('settings.change-password', [])->html();
} elseif ($_instance->childHasBeenRendered('OH4E0Nh')) {
    $componentId = $_instance->getRenderedChildComponentId('OH4E0Nh');
    $componentTag = $_instance->getRenderedChildComponentTagName('OH4E0Nh');
    $html = \Livewire\Livewire::dummyMount($componentId, $componentTag);
    $_instance->preserveRenderedChild('OH4E0Nh');
} else {
    $response = \Livewire\Livewire::mount('settings.change-password', []);
    $html = $response->html();
    $_instance->logRenderedChild('OH4E0Nh', $response->id(), \Livewire\Livewire::getRootElementTagName($html));
}
echo $html;
?>

    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<?php echo \Livewire\Livewire::styles(); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<?php echo \Livewire\Livewire::scripts(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/settings/change-password.blade.php ENDPATH**/ ?>