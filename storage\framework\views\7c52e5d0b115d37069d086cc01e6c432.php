<form wire:submit.prevent="store">
    <div class="card-body">
        

        <div class="row">

            <div class="form-group col-12">
                <label class="font-size-lg">Old Password <span class="text-danger">*</span></label>
                <?php if (isset($component)) { $__componentOriginale8f67e88a74a5189a05256e90291c5aa = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale8f67e88a74a5189a05256e90291c5aa = $attributes; } ?>
<?php $component = App\View\Components\InputWithLoader::resolve(['target' => 'old_password'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('input-with-loader'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\InputWithLoader::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <input type="password" autocomplete="off" wire:model.lazy="old_password" class="form-control" placeholder="Enter Old Password" />
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale8f67e88a74a5189a05256e90291c5aa)): ?>
<?php $attributes = $__attributesOriginale8f67e88a74a5189a05256e90291c5aa; ?>
<?php unset($__attributesOriginale8f67e88a74a5189a05256e90291c5aa); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale8f67e88a74a5189a05256e90291c5aa)): ?>
<?php $component = $__componentOriginale8f67e88a74a5189a05256e90291c5aa; ?>
<?php unset($__componentOriginale8f67e88a74a5189a05256e90291c5aa); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginal7ec225ec96001a00becbb6d24d977476 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7ec225ec96001a00becbb6d24d977476 = $attributes; } ?>
<?php $component = App\View\Components\Error::resolve(['name' => 'old_password'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Error::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7ec225ec96001a00becbb6d24d977476)): ?>
<?php $attributes = $__attributesOriginal7ec225ec96001a00becbb6d24d977476; ?>
<?php unset($__attributesOriginal7ec225ec96001a00becbb6d24d977476); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7ec225ec96001a00becbb6d24d977476)): ?>
<?php $component = $__componentOriginal7ec225ec96001a00becbb6d24d977476; ?>
<?php unset($__componentOriginal7ec225ec96001a00becbb6d24d977476); ?>
<?php endif; ?>
            </div>

            <div class="form-group col-12">
                <label class="font-size-lg">New Password <span class="text-danger">*</span></label>

                <?php if (isset($component)) { $__componentOriginale8f67e88a74a5189a05256e90291c5aa = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale8f67e88a74a5189a05256e90291c5aa = $attributes; } ?>
<?php $component = App\View\Components\InputWithLoader::resolve(['target' => 'password'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('input-with-loader'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\InputWithLoader::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <input type="password" autocomplete="new-password" wire:model.lazy="password" class="form-control" placeholder="Enter New Password" />
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale8f67e88a74a5189a05256e90291c5aa)): ?>
<?php $attributes = $__attributesOriginale8f67e88a74a5189a05256e90291c5aa; ?>
<?php unset($__attributesOriginale8f67e88a74a5189a05256e90291c5aa); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale8f67e88a74a5189a05256e90291c5aa)): ?>
<?php $component = $__componentOriginale8f67e88a74a5189a05256e90291c5aa; ?>
<?php unset($__componentOriginale8f67e88a74a5189a05256e90291c5aa); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginal7ec225ec96001a00becbb6d24d977476 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7ec225ec96001a00becbb6d24d977476 = $attributes; } ?>
<?php $component = App\View\Components\Error::resolve(['name' => 'password'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Error::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7ec225ec96001a00becbb6d24d977476)): ?>
<?php $attributes = $__attributesOriginal7ec225ec96001a00becbb6d24d977476; ?>
<?php unset($__attributesOriginal7ec225ec96001a00becbb6d24d977476); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7ec225ec96001a00becbb6d24d977476)): ?>
<?php $component = $__componentOriginal7ec225ec96001a00becbb6d24d977476; ?>
<?php unset($__componentOriginal7ec225ec96001a00becbb6d24d977476); ?>
<?php endif; ?>
            </div>

            <div class="form-group mb-0 col-12">
                <label class="font-size-lg">Re-enter New Password <span class="text-danger">*</span></label>
                <?php if (isset($component)) { $__componentOriginale8f67e88a74a5189a05256e90291c5aa = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale8f67e88a74a5189a05256e90291c5aa = $attributes; } ?>
<?php $component = App\View\Components\InputWithLoader::resolve(['target' => 'password_confirmation'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('input-with-loader'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\InputWithLoader::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <input type="password" autocomplete="new-password" wire:model.lazy="password_confirmation" class="form-control" placeholder="Re-enter New Password" />
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale8f67e88a74a5189a05256e90291c5aa)): ?>
<?php $attributes = $__attributesOriginale8f67e88a74a5189a05256e90291c5aa; ?>
<?php unset($__attributesOriginale8f67e88a74a5189a05256e90291c5aa); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale8f67e88a74a5189a05256e90291c5aa)): ?>
<?php $component = $__componentOriginale8f67e88a74a5189a05256e90291c5aa; ?>
<?php unset($__componentOriginale8f67e88a74a5189a05256e90291c5aa); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginal7ec225ec96001a00becbb6d24d977476 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7ec225ec96001a00becbb6d24d977476 = $attributes; } ?>
<?php $component = App\View\Components\Error::resolve(['name' => 'password_confirmation'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Error::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7ec225ec96001a00becbb6d24d977476)): ?>
<?php $attributes = $__attributesOriginal7ec225ec96001a00becbb6d24d977476; ?>
<?php unset($__attributesOriginal7ec225ec96001a00becbb6d24d977476); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7ec225ec96001a00becbb6d24d977476)): ?>
<?php $component = $__componentOriginal7ec225ec96001a00becbb6d24d977476; ?>
<?php unset($__componentOriginal7ec225ec96001a00becbb6d24d977476); ?>
<?php endif; ?>
            </div>

        </div>
    </div>

    <div class="card-footer">
        <?php if (isset($component)) { $__componentOriginald2555272519db269fb0587805fbfa3da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald2555272519db269fb0587805fbfa3da = $attributes; } ?>
<?php $component = App\View\Components\BtnWithLoading::resolve(['target' => 'store','text' => 'Save','type' => 'submit'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('btn-with-loading'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\BtnWithLoading::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'store']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald2555272519db269fb0587805fbfa3da)): ?>
<?php $attributes = $__attributesOriginald2555272519db269fb0587805fbfa3da; ?>
<?php unset($__attributesOriginald2555272519db269fb0587805fbfa3da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald2555272519db269fb0587805fbfa3da)): ?>
<?php $component = $__componentOriginald2555272519db269fb0587805fbfa3da; ?>
<?php unset($__componentOriginald2555272519db269fb0587805fbfa3da); ?>
<?php endif; ?>
    </div>

</form>

<script>
    document.addEventListener('livewire:load', function () {
        // Listen for the passwordChanged event
        Livewire.on('passwordChanged', function () {
            // Wait 2 seconds to allow the success message to be displayed
            setTimeout(function () {
                // Call the logout method
                window.livewire.find('<?php echo e($_instance->id); ?>').logout();
            }, 2000);
        });
    });
</script>
<?php /**PATH C:\KodeCreators\newlife-panel\resources\views/livewire/settings/change-password.blade.php ENDPATH**/ ?>