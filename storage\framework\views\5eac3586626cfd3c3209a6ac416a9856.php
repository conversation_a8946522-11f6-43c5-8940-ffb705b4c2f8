

<?php $__env->startSection('content'); ?>
    <div class="card card-custom mb-5">


        <div class="card-body" x-data="{ showFilter: false }">

            <div class="row justify-content-between ">
                <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                    <div class="input-icon">
                        <input type="text" class="form-control" placeholder="Search..." id="medications_search" />
                        <span>
                            <i class="flaticon2-search-1 text-muted"></i>
                        </span>
                    </div>
                </div>
                <div class="col-12 col-sm">
                    <a href="<?php echo e(route('practices.medications.create', ['practice' => $practice->id])); ?>">
                        <button type="button" class="btn btn-primary" id="add-box-btn">
                            <i class="fa fa-plus"></i>
                            <span>Connect Medication</span>
                        </button>
                    </a>
                </div>
            </div>

            <div class="datatable datatable-bordered datatable-head-custom" id="medications_dt"></div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
    <script>
        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;

        const storagePath = `<?php echo e(url('/storage')); ?>`;
        let url = "<?php echo e(Storage::url('/')); ?>";
        const apiRoute = `<?php echo e(route('practices.api.medications', ['practice' => $practice->id])); ?>`;
        const editRoute = `<?php echo e(route('practices.medications.edit', ['practice' => $practice->id, 'medication' => '::ID'])); ?>`;
        const deleteRoute = `<?php echo e(route('practices.medications.delete', ['practice' => $practice->id, 'medication' => '::ID'])); ?>`;

        datatableElement = $('#medications_dt');
        searchElement = $('#medications_search');

        columnArray = [
            {
                field: 'medication.name',
                title: `Name`,
                width: 'auto',
                sortable: true,
            },
            {
                field: 'medication.ndc',
                title: `NDC`,
                width: 'auto',
                sortable: true,
            },
            {
                field: 'practice_medications.created_at',
                title: `Created At`,
                width: 'auto',
                sortable: true,
                template: function (data) {
                    return data.created_at ? moment(data.created_at).format('MM/DD/YYYY hh:mm A') : '';
                }
            },
            {
                field: 'action',
                title: `Action`,
                width: 'auto',
                sortable: false,
                template: function (data) {
                    return `<a href="${deleteRoute.replace('::ID', data.id)}" class="btn btn-sm btn-clean btn-icon delete-btn" data-toggle="tooltip" title="Delete" data-id="${data.id}">
                                    <i class="menu-icon fas fa-trash"></i>
                                </a>
                            `;
                }
            }
        ];

        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute,
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        map: function (raw) {
                            var dataSet = raw;
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;
                            }
                            return dataSet;
                        },
                    },
                },
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pagination: true,
            search: {
                input: searchElement,
                key: 'search'
            },
            layout: {
                customScrollbar: false,
                scroll: true,
            },
            columns: columnArray
        });

        $(document).on('click', '.delete-btn', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            Swal.fire({
                title: 'Are you sure?',
                text: "Delete medication?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, Delete!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: deleteRoute.replace('::ID', id),
                        type: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        success: function (response) {
                            toastr.success(response.message);
                            datatable.reload();
                        },
                        error: function (response) {
                            toastr.error(response.message);
                        }
                    });
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/practices/index-medications.blade.php ENDPATH**/ ?>