

<?php $__env->startSection('content'); ?>
    <div class="card card-custom mb-5">

        <div class="card-body" x-data="{ showFilter: false }">

            <div class="row justify-content-between ">
                <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                    <div class="input-icon">
                        <input type="text" class="form-control" placeholder="Search..." id="practices_search" />
                        <span>
                            <i class="flaticon2-search-1 text-muted"></i>
                        </span>
                    </div>
                </div>
                <div class="col-12 col-sm">
                    <a href="<?php echo e(route('practices.providers.create', $practice->id)); ?>">
                        <button type="button" class="btn btn-primary" id="add-box-btn">
                            <i class="fa fa-plus"></i>
                            <span>Connect Provider</span>
                        </button>
                    </a>
                </div>
            </div>

            <div class="datatable datatable-bordered datatable-head-custom" id="practice_providers_dt"></div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
    <script>
        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;

        const apiRoute = `<?php echo e(route('practices.providers.api', $practice->id)); ?>`;
        let url = "<?php echo e(Storage::url('/')); ?>";
        const csrfToken = `<?php echo e(csrf_token()); ?>`;
        const deleteRoute = `<?php echo e(route('practices.providers.delete', ['practice' => $practice->id, 'provider' => '::ID'])); ?>`;
        
        datatableElement = $('#practice_providers_dt');
        searchElement = $('#practice_providers_search');

        columnArray = [
            {
                field: 'user.first_name',
                title: `First Name`,
                width: 200,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'user.last_name',
                title: `Last Name`,
                width: 200,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'user.email',
                title: `email`,
                width: 200,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'user.default_dispatch_method',
                title: 'Dispatch Method',
                overflow: 'visible',
                autoHide: false,
                width: 200,
                template: function (row) {
                    return row.user.default_dispatch_method || '-';
                }
            },
            {
                field: 'action',
                title: `Action`,
                width: 'auto',
                sortable: false,
                template: function (data) {
                    return `<a href="${deleteRoute.replace('::ID', data.user.id)}" class="btn btn-sm btn-clean btn-icon delete-btn" data-toggle="tooltip" title="Delete" data-id="${data.user.id}">
                                    <i class="menu-icon fas fa-trash"></i>
                                </a>
                            `;
                }
            },
        ];

        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute,
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        map: function (raw) {
                            var dataSet = raw;
                            console.log(raw.data);
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;
                            }
                            return dataSet;
                        },
                    },
                },
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pagination: true,
            search: {
                input: searchElement,
                key: 'search'
            },
            layout: {
                customScrollbar: false,
                scroll: true,
            },
            columns: columnArray
        });

        $(document).on('click', '.delete-btn', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            Swal.fire({
                title: 'Are you sure?',
                text: "Delete provider?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, Delete!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: deleteRoute.replace('::ID', id),
                        type: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        success: function (response) {
                            toastr.success(response.message);
                            datatable.reload();
                        },
                        error: function (response) {
                            toastr.error(response.message);
                        }
                    });
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/practices/index-providers.blade.php ENDPATH**/ ?>